import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { getTokenStats, getRecentTokenLogs } from "@/lib/token-logger";

/**
 * GET /api/admin/token-usage
 * Returns token usage statistics and recent logs
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const { userId } = getAuth(req);
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized", details: "User not authenticated" },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const includeStats = searchParams.get('stats') !== 'false';
    const includeLogs = searchParams.get('logs') !== 'false';

    const response: any = {};

    // Get token statistics
    if (includeStats) {
      const stats = await getTokenStats();
      response.stats = stats;
    }

    // Get recent token logs
    if (includeLogs) {
      const logs = await getRecentTokenLogs(limit);
      response.logs = logs;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching token usage data:", error);
    return NextResponse.json(
      { error: "Failed to fetch token usage data" },
      { status: 500 }
    );
  }
}
