"use client";

import { ModeToggle } from "@/components/mode-toggle";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogClose } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { UserProfile } from "@/components/user-profile";
import { MenuIcon } from "lucide-react";
import { HomeIcon, Settings } from "lucide-react";
import Link from "next/link";
import { ReactNode } from "react";

export function DashboardTopNav({ children }: { children: ReactNode }) {
  return (
    <div className="flex flex-col">
      <header className="flex h-14 lg:h-[55px] items-center gap-4 border-b px-3">
        <Dialog>
          <SheetTrigger className="min-[1024px]:hidden p-2 transition">
            <MenuIcon />
            <Link href="/dashboard">
              <span className="sr-only">Home</span>
            </Link>
          </SheetTrigger>
          <SheetContent side="left">
            <SheetHeader>
              <Link href="/">
                <SheetTitle>Form Axis</SheetTitle>
              </Link>
            </SheetHeader>
            <div className="flex flex-col space-y-3 mt-[1rem]">
              <DialogClose asChild>
                <Link href="/dashboard">
                  <Button variant="outline" className="w-full">
                    <HomeIcon className="mr-2 h-4 w-4" />
                    Home
                  </Button>
                </Link>
              </DialogClose>

              <Separator className="my-3" />
              <DialogClose asChild>
                <Link href="/dashboard/settings">
                  <Button variant="outline" className="w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                </Link>
              </DialogClose>
            </div>
          </SheetContent>
        </Dialog>
        <div className="flex justify-center items-center gap-2 ml-auto">
          {<UserProfile />}
          <ModeToggle />
        </div>
      </header>
      {children}
    </div>
  );
}
