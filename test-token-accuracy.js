/**
 * Test script to verify token counting accuracy between generateObject and streamObject
 * Run this with: node test-token-accuracy.js
 */

import { google } from '@ai-sdk/google';
import { generateObject, streamObject } from 'ai';
import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Simple test schema
const testSchema = z.object({
  title: z.string(),
  description: z.string(),
  items: z.array(z.string()),
});

const testPrompt = "Create a simple list about healthy eating habits with a title, description, and 5 items.";

async function testTokenAccuracy() {
  console.log("🧪 Testing Token Accuracy: generateObject vs streamObject\n");

  if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    console.error("❌ Please set GOOGLE_GENERATIVE_AI_API_KEY in your .env file");
    return;
  }

  const model = google('gemini-2.5-pro');

  try {
    // Test 1: generateObject (old method)
    console.log("1️⃣ Testing generateObject (old method):");
    const generateResult = await generateObject({
      model,
      schema: testSchema,
      prompt: testPrompt,
    });

    console.log("   Result usage:", generateResult.usage);
    console.log("   Generated title:", generateResult.object.title);
    console.log("   Items count:", generateResult.object.items.length);

    // Test 2: streamObject with onFinish (new method)
    console.log("\n2️⃣ Testing streamObject with onFinish (new method):");
    
    let streamTokenUsage = null;
    const streamResult = streamObject({
      model,
      schema: testSchema,
      prompt: testPrompt,
      onFinish({ usage }) {
        streamTokenUsage = usage;
        console.log("   onFinish usage:", usage);
      },
    });

    const streamObject_result = await streamResult.object;
    console.log("   Generated title:", streamObject_result.title);
    console.log("   Items count:", streamObject_result.items.length);

    // Compare results
    console.log("\n📊 Comparison:");
    console.log("   generateObject tokens:", {
      input: generateResult.usage?.promptTokens || 0,
      output: generateResult.usage?.completionTokens || 0,
      total: generateResult.usage?.totalTokens || 0,
    });
    console.log("   streamObject tokens:", {
      input: streamTokenUsage?.promptTokens || 0,
      output: streamTokenUsage?.completionTokens || 0,
      total: streamTokenUsage?.totalTokens || 0,
    });

    // Calculate differences
    const inputDiff = (streamTokenUsage?.promptTokens || 0) - (generateResult.usage?.promptTokens || 0);
    const outputDiff = (streamTokenUsage?.completionTokens || 0) - (generateResult.usage?.completionTokens || 0);
    const totalDiff = (streamTokenUsage?.totalTokens || 0) - (generateResult.usage?.totalTokens || 0);

    console.log("\n🔍 Differences (streamObject - generateObject):");
    console.log(`   Input tokens: ${inputDiff > 0 ? '+' : ''}${inputDiff}`);
    console.log(`   Output tokens: ${outputDiff > 0 ? '+' : ''}${outputDiff}`);
    console.log(`   Total tokens: ${totalDiff > 0 ? '+' : ''}${totalDiff}`);

    if (Math.abs(inputDiff) > 0 || Math.abs(outputDiff) > 0) {
      console.log("\n✅ Token counts differ - streamObject provides more accurate data!");
    } else {
      console.log("\n🤔 Token counts are the same - both methods reporting identical usage");
    }

  } catch (error) {
    console.error("❌ Error during testing:", error);
  }
}

// Run the test
testTokenAccuracy();
