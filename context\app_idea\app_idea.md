# Genform

## 🎯 Core concept
An AI-powered conversational feedback and reviews platform for local businesses, online brands, and tech startups that gathers richer customer insights through adaptive, human-like chats via web, QR codes, auto-tags sentiment, and turns responses into polished public reviews—all managed from one integrated dashboard.

Instead of a static widget or upvote board:

- A customer says, “Your coffee is great, but the chairs are uncomfortable.” 
- The AI instantly replies, “Thanks for the praise! Could you tell us more about the chairs — is it the height, the cushioning, or something else?”
- For SaaS: A user says, “It’s slow on large files.” AI follows up: “Can you share the typical file size? This will help our dev team reproduce the issue.”

## 🔹 Problems this solves (better than traditional forms/boards)

1. Low-quality feedback → AI probes for specifics automatically.
2. Drop-offs → Conversational tone feels more like chatting than “work.”
3. One-size-fits-all forms → Dynamic questions adapt to user type, sentiment, and context.
4. Multi-industry adaptability → Same engine works for local businesses like cafés, online businesses, e-commerce, SaaS, etc., without custom building new forms.

## Why it’s a great idea

- Clear problem & audience → You’re targeting two huge groups that already rely heavily on feedback: local businesses (low-tech, high-customer-touch) and online/tech companies (feedback-driven product cycles).

- Low friction → URLs, widgets, and QR codes are dead-simple entry points — no app downloads, no login required for customers.

- Differentiation → Unlike Google Forms or Canny-style boards, this feels modern and engaging with the AI conversational flow.

- Dual value → Serves as both an internal improvement tool and an external review generator, so businesses can improve operations and boost reputation in one step.

- Viral growth potential → QR codes on receipts, menus, or packaging can quietly market your tool every time a customer interacts.

## Core tech stack

- Frontend: Next.js 15 + Vercel AI SDK + Vercel’s Generative UI (live AI-powered editing and preview)
- AI model: Google Gemini via Vertex AI(with Vercel AI SDK)
- Database and storage: Supabase
- Vector DB: Supabase pgvector (context enrichment from docs or scraped data)
- Content ingestion: Firecrawl for pulling business-specific info from the web and crawling the business's websites and resources to gather information on their businesses/company/startup
- Hosting: Cloudflare for deployment
- Payment processing: Polar.sh with Per month and per conversation pricing

## Planned features and main components

1. Editor

- AI-assisted form creation (choose tone, welcome screen, journey flow, visuals, about you/company)
- Live preview and form testing sandbox (no cost token usage for test mode — can mock the AI)
- Save & publish with version history

### Live Preview Implementation
- **Dual URL Strategy**:
  - Preview URL: `/forms/{formId}/preview?version=draft` (for editor preview pane)
  - Published URL: `/f/{slug}` (for public access)
- **Split-pane Layout**: Editor on left, live preview iframe on right
- **Real-time Updates**: postMessage communication between editor and preview iframe
- **Same Runtime Component**: Preview uses identical form runtime as production but with:
  - Draft configuration (not published config)
  - Separate API key for preview AI provider.
  - Auth-gated access (org members only)
  - No indexing/discovery
- **Perfect Parity**: What you see in preview is exactly what users get when published
- **Security**: Same-origin iframe with CSP headers and sandbox attributes
- **Performance**: Lazy loading, debounced updates, streaming responses

2. Conversations
- View each conversation transcript
- Metadata:
    - User name (or “Anonymous” if no name given)
    - Sentiment score (positive, neutral, negative — AI classified)
    - AI-generated summary paragraph of the conversation
- Key info table:
    - Core form answers
    - “Additional Information” section for extra context captured during the chat

3. Analytics

- Metrics dashboard:
    - Completion rate %
    - Partial completion %
    - Abandonment rate %
    - Average response time
    - Popular questions/flows
    - AI sentiment distribution over time

4. Publishing

- Embed options:
    - Inline section
    - Full page
    - Popup / popover widget
    - Banner
- Direct link URL
- Auto-generated QR code for physical use cases (restaurants, stores, events)

## Strengths
### Focused Use Case
- By going all-in on reviews, lead capture, and feedback, you can market directly to small businesses, SaaS startups, and service providers — they’ll immediately “get” why it’s valuable.
- Formless, Typeform, and others go too broad, so your messaging can be much sharper.

### Live Preview Without Cost
That “live AI preview without API usage” is very appealing. Businesses hate burning credits just to test forms.

### Sentiment + Summary + Key Info Extraction
Most form/chat analytics are basic (“x completions”), but you’re talking about qualitative insights too, which owners love because it’s easy to act on.

### Multiple Deploy Methods + QR Codes
QR code publishing is genius for local businesses — think cafés, real estate, salons, gyms — instant feedback capture without heavy tech.

### Embeddable in Multiple Styles
Inline, popover, banner, full-page — covers a lot of use cases without forcing them into one layout.