import { getFormGenPrompt } from "@/lib/prompts/form-gen-prompt";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { FormGeneration, formGenerationSchema } from "@/lib/schema";
import { logTokenUsage } from "@/lib/token-logger";

/**
 * Token usage information for monitoring
 */
export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  model: string;
  timestamp: string;
  prompt: string;
  topics?: string;
}

/**
 * Form generation result with token usage
 */
export interface FormGenerationResult {
  form: FormGeneration;
  tokenUsage: TokenUsage;
}

/**
 * Generates form questions based on the provided prompt using Google's Gemini model
 * @param prompt - User prompt describing the form they want to create
 * @param topics - Optional topics to guide the form generation
 * @returns Generated form with title, description, questions, and token usage
 */
export async function generateFormQuestions(
  prompt: string,
  topics?: string
): Promise<FormGenerationResult> {
  if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    throw new Error("Please don't forget to set your Google API key");
  }

  try {
    const formGenerationPrompt = getFormGenPrompt({
      prompt,
      topics,
    });

    const modelName = "gemini-2.5-pro";
    const startTime = new Date().toISOString();

    const result = await generateObject({
      model: google(modelName, {
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
        ],
      }),
      schema: formGenerationSchema,
      prompt: formGenerationPrompt,
      temperature: 0.7,
    });

    const formData = result.object;

    // Extract token usage - prioritize the most complete data available
    const tokenUsageFromResult = {
      promptTokens: result.usage?.promptTokens || 0,
      completionTokens: result.usage?.completionTokens || 0,
      totalTokens: result.usage?.totalTokens || 0,
    };

    // Log detailed response for debugging token discrepancies
    console.log("🔍 Complete generateObject Response:", {
      usage: result.usage,
      response: result.response,
      tokenUsage: tokenUsageFromResult,
      promptLength: formGenerationPrompt.length,
      promptPreview: formGenerationPrompt.substring(0, 200) + "...",
    });

    // Additional debugging: Check if there are any hidden token usage fields
    if (result.response && typeof result.response === 'object') {
      const responseObj = result.response as any;
      console.log("🔍 Response Object Keys:", Object.keys(responseObj));

      // Check for Google AI specific usage metadata
      if (responseObj.body) {
        console.log("🔍 Response Body:", responseObj.body);
      }
      if (responseObj.headers) {
        console.log("🔍 Response Headers:", responseObj.headers);
      }
    }

    const tokenUsage: TokenUsage = {
      inputTokens: tokenUsageFromResult.promptTokens,
      outputTokens: tokenUsageFromResult.completionTokens,
      totalTokens: tokenUsageFromResult.totalTokens,
      model: modelName,
      timestamp: startTime,
      prompt: prompt,
      topics: topics,
    };

    // Log token usage for monitoring (console)
    console.log("🔍 Form Generation Token Usage:", JSON.stringify({
      ...tokenUsage,
      formTitle: formData.title,
      questionCount: formData.questions.length,
      tokenSource: "generateObject result.usage",
    }, null, 2));

    // Log token usage to file for persistent monitoring
    await logTokenUsage(
      tokenUsage,
      formData.title,
      formData.questions.length,
      false, // Not estimated - using actual SDK data
      tokenUsageFromResult.promptTokens === 0 ? "No token usage reported by generateObject" : undefined
    );

    const formGeneration: FormGeneration = {
      title: formData.title,
      description: formData.description,
      questions: formData.questions.map((q) => ({
        content: q.content,
        type: q.type,
        required: q.required,
      })),
    };

    return {
      form: formGeneration,
      tokenUsage: tokenUsage,
    };
  } catch (error) {
    console.error("Error generating form questions:", error);
    throw new Error(
      "Failed to generate form questions. Please try again later."
    );
  }
}

/**
 * Utility function to shorten and sanitize a string for use as a URL slug
 * @param str - The string to convert to a slug
 * @returns A URL-friendly slug string
 */
export function generateSlug(str: string): string {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "")
    .substring(0, 50); // Limit length
}
