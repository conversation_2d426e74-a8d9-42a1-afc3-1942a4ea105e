import { getFormGenPrompt } from "@/lib/prompts/form-gen-prompt";
import { google } from "@ai-sdk/google";
import { streamObject } from "ai";
import { FormGeneration, formGenerationSchema } from "@/lib/schema";
import { logTokenUsage } from "@/lib/token-logger";

/**
 * Token usage information for monitoring
 */
export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  model: string;
  timestamp: string;
  prompt: string;
  topics?: string;
}

/**
 * Form generation result with token usage
 */
export interface FormGenerationResult {
  form: FormGeneration;
  tokenUsage: TokenUsage;
}

/**
 * Generates form questions based on the provided prompt using Google's Gemini model
 * @param prompt - User prompt describing the form they want to create
 * @param topics - Optional topics to guide the form generation
 * @returns Generated form with title, description, questions, and token usage
 */
export async function generateFormQuestions(
  prompt: string,
  topics?: string
): Promise<FormGenerationResult> {
  if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    throw new Error("Please don't forget to set your Google API key");
  }

  try {
    const formGenerationPrompt = getFormGenPrompt({
      prompt,
      topics,
    });

    const modelName = "gemini-2.5-pro";
    const startTime = new Date().toISOString();

    // Variable to store accurate token usage from onFinish callback
    let accurateTokenUsage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    } | null = null;

    const result = streamObject({
      model: google(modelName, {
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE",
          },
        ],
      }),
      schema: formGenerationSchema,
      prompt: formGenerationPrompt,
      temperature: 0.7,
      onFinish({ usage }) {
        // This callback provides the most accurate token usage data
        accurateTokenUsage = {
          promptTokens: usage.promptTokens,
          completionTokens: usage.completionTokens,
          totalTokens: usage.totalTokens,
        };

        console.log("🎯 Accurate Token Usage from onFinish:", {
          inputTokens: usage.promptTokens,
          outputTokens: usage.completionTokens,
          totalTokens: usage.totalTokens,
          timestamp: new Date().toISOString(),
        });
      },
    });

    // Wait for the streaming to complete and get the final object
    const formData = await result.object;

    // Use the accurate token usage from onFinish callback
    const tokenUsageFromResult = accurateTokenUsage || {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
    };

    // Log comparison between old and new method
    console.log("🔍 Token Usage Comparison:", {
      accurateUsage: tokenUsageFromResult,
      promptLength: formGenerationPrompt.length,
      promptPreview: formGenerationPrompt.substring(0, 200) + "...",
      method: "streamObject with onFinish callback",
    });

    const tokenUsage: TokenUsage = {
      inputTokens: tokenUsageFromResult.promptTokens,
      outputTokens: tokenUsageFromResult.completionTokens,
      totalTokens: tokenUsageFromResult.totalTokens,
      model: modelName,
      timestamp: startTime,
      prompt: prompt,
      topics: topics,
    };

    // Log token usage for monitoring (console) with improved accuracy info
    console.log("🎯 Accurate Form Generation Token Usage:", JSON.stringify({
      ...tokenUsage,
      formTitle: formData.title,
      questionCount: formData.questions.length,
      tokenSource: "streamObject onFinish callback",
      accuracy: "High - Direct from Gemini API",
      promptCharacters: formGenerationPrompt.length,
      estimatedTokensFromChars: Math.ceil(formGenerationPrompt.length / 4), // Rough estimate for comparison
    }, null, 2));

    // Log token usage to file for persistent monitoring
    await logTokenUsage(
      tokenUsage,
      formData.title,
      formData.questions.length,
      false, // Not estimated - using actual accurate SDK data
      tokenUsageFromResult.promptTokens === 0 ? "No token usage reported by streamObject onFinish" : "Accurate data from streamObject onFinish callback"
    );

    const formGeneration: FormGeneration = {
      title: formData.title,
      description: formData.description,
      questions: formData.questions.map((q: any) => ({
        content: q.content,
        type: q.type,
        required: q.required,
      })),
    };

    return {
      form: formGeneration,
      tokenUsage: tokenUsage,
    };
  } catch (error) {
    console.error("Error generating form questions:", error);
    throw new Error(
      "Failed to generate form questions. Please try again later."
    );
  }
}

/**
 * Utility function to shorten and sanitize a string for use as a URL slug
 * @param str - The string to convert to a slug
 * @returns A URL-friendly slug string
 */
export function generateSlug(str: string): string {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "")
    .substring(0, 50); // Limit length
}
