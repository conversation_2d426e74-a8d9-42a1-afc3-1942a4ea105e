[{"inputTokens": 631, "outputTokens": 390, "totalTokens": 1021, "model": "gemini-2.5-pro", "timestamp": "2025-08-12T14:56:24.960Z", "prompt": "A survey for newly released coffee flavour\n", "topics": "-price\n-taste\n-service", "id": "1755010615288-971xuw1xk", "formTitle": "Feedback on Our New Coffee Flavor", "questionCount": 7, "costEstimate": 0.00032865}, {"inputTokens": 652, "outputTokens": 411, "totalTokens": 1063, "model": "gemini-2.5-pro", "timestamp": "2025-08-12T15:01:17.647Z", "prompt": "a servey on the taste of newly released chicken noodle recipe", "topics": "- taste\n- quality\n- price\n- any suggestions?\n- toppings needed?\n- any new flavours?\n", "id": "1755010894838-jln05bbu7", "formTitle": "New Chicken Noodle Recipe <PERSON>back", "questionCount": 7, "costEstimate": 0.00034439999999999997}, {"inputTokens": 636, "outputTokens": 329, "totalTokens": 965, "model": "gemini-2.5-pro", "timestamp": "2025-08-12T15:39:18.337Z", "prompt": "a servey to get review on the new chocolate shake\n", "topics": "-taste\n-price\n-suggestions?", "id": "1755013189498-gctk6ri0y", "formTitle": "New Chocolate Shake Feedback", "questionCount": 6, "costEstimate": 0.00029279999999999996, "isEstimated": false, "discrepancyNote": "AI SDK reported 636 input tokens vs estimated 717 (81 difference)"}, {"inputTokens": 636, "outputTokens": 417, "totalTokens": 1053, "model": "gemini-2.5-pro", "timestamp": "2025-08-12T15:42:12.774Z", "prompt": "a syrvey to see reviews of new macha tea\n", "topics": "-price\n-taste\n-quality", "id": "1755013354938-j9tcl67uj", "formTitle": "New Matcha Tea Feedback", "questionCount": 8, "costEstimate": 0.00034559999999999994, "isEstimated": false, "discrepancyNote": "AI SDK reported 636 input tokens vs estimated 713 (77 difference)"}, {"inputTokens": 633, "outputTokens": 442, "totalTokens": 1075, "model": "gemini-2.5-pro", "timestamp": "2025-08-12T17:12:51.401Z", "prompt": "a form for review on new shoe\n", "topics": "- quality\n- price\n- worth it?", "id": "1755018790096-a73z5n1e2", "formTitle": "Tell Us What You Think About Our New Shoe!", "questionCount": 8, "costEstimate": 0.00036015, "isEstimated": false}, {"inputTokens": 637, "outputTokens": 362, "totalTokens": 999, "model": "gemini-2.5-pro", "timestamp": "2025-08-12T17:18:25.245Z", "prompt": " a form to get info on feedback for newly opened pizza restaurant\n", "topics": "- service\n- food\n-quality", "id": "1755019120790-qyvwq3nus", "formTitle": "Pizza Restaurant Feedback Form", "questionCount": 7, "costEstimate": 0.00031275, "isEstimated": false}]