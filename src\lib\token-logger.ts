import { writeFile, readFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { TokenUsage } from '@/app/api/forms/generate/api';

/**
 * Extended token usage with additional metadata
 */
export interface TokenUsageLog extends TokenUsage {
  id: string;
  formTitle?: string;
  questionCount?: number;
  costEstimate?: number;
}

/**
 * Token usage statistics
 */
export interface TokenStats {
  totalRequests: number;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalTokens: number;
  averageInputTokens: number;
  averageOutputTokens: number;
  estimatedTotalCost: number;
  lastUpdated: string;
}

const LOGS_DIR = path.join(process.cwd(), 'logs');
const TOKEN_LOG_FILE = path.join(LOGS_DIR, 'token-usage.json');
const STATS_FILE = path.join(LOGS_DIR, 'token-stats.json');

/**
 * Estimated cost per 1K tokens for Gemini models (approximate)
 */
const GEMINI_COST_PER_1K_TOKENS = {
  input: 0.00015, // $0.00015 per 1K input tokens
  output: 0.0006, // $0.0006 per 1K output tokens
};

/**
 * Calculate estimated cost for token usage
 */
function calculateCost(inputTokens: number, outputTokens: number): number {
  const inputCost = (inputTokens / 1000) * GEMINI_COST_PER_1K_TOKENS.input;
  const outputCost = (outputTokens / 1000) * GEMINI_COST_PER_1K_TOKENS.output;
  return inputCost + outputCost;
}

/**
 * Ensure logs directory exists
 */
async function ensureLogsDir(): Promise<void> {
  if (!existsSync(LOGS_DIR)) {
    await mkdir(LOGS_DIR, { recursive: true });
  }
}

/**
 * Read existing token logs
 */
async function readTokenLogs(): Promise<TokenUsageLog[]> {
  try {
    if (!existsSync(TOKEN_LOG_FILE)) {
      return [];
    }
    const data = await readFile(TOKEN_LOG_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading token logs:', error);
    return [];
  }
}

/**
 * Write token logs to file
 */
async function writeTokenLogs(logs: TokenUsageLog[]): Promise<void> {
  try {
    await ensureLogsDir();
    await writeFile(TOKEN_LOG_FILE, JSON.stringify(logs, null, 2));
  } catch (error) {
    console.error('Error writing token logs:', error);
  }
}

/**
 * Calculate and save token statistics
 */
async function updateTokenStats(logs: TokenUsageLog[]): Promise<void> {
  if (logs.length === 0) return;

  const stats: TokenStats = {
    totalRequests: logs.length,
    totalInputTokens: logs.reduce((sum, log) => sum + log.inputTokens, 0),
    totalOutputTokens: logs.reduce((sum, log) => sum + log.outputTokens, 0),
    totalTokens: logs.reduce((sum, log) => sum + log.totalTokens, 0),
    averageInputTokens: 0,
    averageOutputTokens: 0,
    estimatedTotalCost: 0,
    lastUpdated: new Date().toISOString(),
  };

  stats.averageInputTokens = Math.round(stats.totalInputTokens / stats.totalRequests);
  stats.averageOutputTokens = Math.round(stats.totalOutputTokens / stats.totalRequests);
  stats.estimatedTotalCost = logs.reduce((sum, log) => sum + (log.costEstimate || 0), 0);

  try {
    await ensureLogsDir();
    await writeFile(STATS_FILE, JSON.stringify(stats, null, 2));
  } catch (error) {
    console.error('Error writing token stats:', error);
  }
}

/**
 * Log token usage to file
 */
export async function logTokenUsage(
  tokenUsage: TokenUsage,
  formTitle?: string,
  questionCount?: number,
  isEstimated?: boolean,
  discrepancyNote?: string
): Promise<void> {
  const costEstimate = calculateCost(tokenUsage.inputTokens, tokenUsage.outputTokens);

  const logEntry: TokenUsageLog = {
    ...tokenUsage,
    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    formTitle,
    questionCount,
    costEstimate,
    isEstimated,
    discrepancyNote,
  };

  try {
    const existingLogs = await readTokenLogs();
    existingLogs.push(logEntry);
    
    // Keep only the last 1000 entries to prevent file from growing too large
    const trimmedLogs = existingLogs.slice(-1000);
    
    await writeTokenLogs(trimmedLogs);
    await updateTokenStats(trimmedLogs);
    
    console.log('💾 Token usage logged to file:', {
      file: TOKEN_LOG_FILE,
      entry: logEntry,
    });
  } catch (error) {
    console.error('Error logging token usage:', error);
  }
}

/**
 * Get token usage statistics
 */
export async function getTokenStats(): Promise<TokenStats | null> {
  try {
    if (!existsSync(STATS_FILE)) {
      return null;
    }
    const data = await readFile(STATS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading token stats:', error);
    return null;
  }
}

/**
 * Get recent token usage logs
 */
export async function getRecentTokenLogs(limit: number = 50): Promise<TokenUsageLog[]> {
  try {
    const logs = await readTokenLogs();
    return logs.slice(-limit).reverse(); // Return most recent first
  } catch (error) {
    console.error('Error reading recent token logs:', error);
    return [];
  }
}
