"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { RefreshCw, Download, TrendingUp, Zap, DollarSign, Clock } from "lucide-react";
import { toast } from "sonner";

interface TokenUsageLog {
  id: string;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  model: string;
  timestamp: string;
  prompt: string;
  topics?: string;
  formTitle?: string;
  questionCount?: number;
  costEstimate?: number;
}

interface TokenStats {
  totalRequests: number;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalTokens: number;
  averageInputTokens: number;
  averageOutputTokens: number;
  estimatedTotalCost: number;
  lastUpdated: string;
}

interface TokenUsageData {
  stats: TokenStats | null;
  logs: TokenUsageLog[];
}

export function TokenUsageDashboard() {
  const [data, setData] = useState<TokenUsageData | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchTokenUsage = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/token-usage');
      if (!response.ok) {
        throw new Error('Failed to fetch token usage data');
      }
      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching token usage:', error);
      toast.error('Failed to load token usage data');
    } finally {
      setLoading(false);
    }
  };

  const exportData = () => {
    if (!data) return;
    
    const exportData = {
      stats: data.stats,
      logs: data.logs,
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `token-usage-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Token usage data exported');
  };

  useEffect(() => {
    fetchTokenUsage();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Token Usage Dashboard</h2>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const { stats, logs } = data || {};

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Token Usage Dashboard</h2>
        <div className="flex gap-2">
          <Button onClick={fetchTokenUsage} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={exportData} variant="outline" size="sm" disabled={!data}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalRequests}</div>
              <p className="text-xs text-muted-foreground">
                Forms generated
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTokens.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalInputTokens.toLocaleString()} input + {stats.totalOutputTokens.toLocaleString()} output
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${stats.estimatedTotalCost.toFixed(4)}</div>
              <p className="text-xs text-muted-foreground">
                Approximate total cost
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Tokens/Request</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round((stats.totalTokens / stats.totalRequests) || 0)}</div>
              <p className="text-xs text-muted-foreground">
                {stats.averageInputTokens} in + {stats.averageOutputTokens} out
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Token Usage</CardTitle>
          <CardDescription>
            Latest form generation requests and their token consumption
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logs && logs.length > 0 ? (
            <div className="space-y-4">
              {logs.map((log) => (
                <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{log.formTitle || 'Untitled Form'}</h4>
                      <Badge variant="secondary">{log.questionCount} questions</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {log.prompt.length > 100 ? `${log.prompt.substring(0, 100)}...` : log.prompt}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>{new Date(log.timestamp).toLocaleString()}</span>
                      <span>{log.model}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{log.totalTokens.toLocaleString()} tokens</div>
                    <div className="text-xs text-muted-foreground">
                      {log.inputTokens.toLocaleString()} in / {log.outputTokens.toLocaleString()} out
                    </div>
                    {log.costEstimate && (
                      <div className="text-xs text-green-600">
                        ~${log.costEstimate.toFixed(4)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">
              No token usage data available yet. Generate some forms to see usage statistics.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
