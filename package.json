{"name": "form-axis", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:convex": "convex dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/google": "^1.2.10", "@ai-sdk/react": "^1.2.8", "@clerk/clerk-react": "^5.27.0", "@clerk/nextjs": "^6.14.3", "@convex-dev/migrations": "^0.2.5", "@hookform/resolvers": "^5.0.1", "@icons-pack/react-simple-icons": "^12.6.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@tanstack/react-table": "^8.21.2", "@types/canvas-confetti": "^1.9.0", "@vercel/analytics": "^1.5.0", "ai": "^4.3.5", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "convex": "^1.23.0", "date-fns": "^4.1.0", "dedent": "^1.5.3", "lucide-react": "^0.487.0", "motion": "^12.6.5", "next": "15.3.0", "next-themes": "^0.4.6", "papaparse": "^5.5.2", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4", "typescript": "^5"}}