import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { ConvexClerkProvider } from "./provider";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import { Analytics } from "@vercel/analytics/next";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Form Axis | AI-Powered Form Builder",
  description:
    "Create forms by simply writing prompts. Share a link and let users answer naturally through a chat interface.",
  keywords: "AI, forms, chat interface, user experience",
  authors: [{ name: "<PERSON><PERSON><PERSON>", url: "https://adarsha.dev" }],
  creator: "<PERSON><PERSON><PERSON>",
  openGraph: {
    title: "Form Axis | AI-Powered Form Builder",
    description:
      "Create forms by simply writing prompts. Share a link and let users answer naturally through a chat interface.",
    url: "https://formaxis.dev",
    siteName: "Form Axis",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider dynamic>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <ConvexClerkProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="dark"
              enableSystem
              disableTransitionOnChange
            >
              {children}
              <Analytics />
              <Toaster position="top-right" />
            </ThemeProvider>
          </ConvexClerkProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
